2025-06-09 03:56:11 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 03:56:11 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 03:56:11 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 03:56:11 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 03:56:11 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 03:56:11 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 03:56:11 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 03:56:11 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 03:56:11 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 03:56:11 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 03:56:11 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 03:56:14 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 03:56:14 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 03:56:14 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 03:56:14 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 03:56:15 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI207682\logo_Sofrecom.png
2025-06-09 03:56:15 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 03:56:15 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 03:56:15 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 03:56:15 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:56:15 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 03:56:15 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 03:56:16 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 03:56:16 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:56:16 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 03:56:16 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 03:56:16 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 03:56:16 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 03:56:16 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 03:56:16 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 03:56:27 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 03:56:27 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 03:56:27 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 03:56:27 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 03:56:27 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 03:56:27 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 03:56:27 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 03:56:27 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 03:56:29 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 6 communes to update
2025-06-09 03:56:31 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:56:31 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:56:32 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 03:56:32 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 03:56:32 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 03:56:32 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 03:56:32 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 03:56:32 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 03:56:32 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 03:56:32 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 98 rows
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 553 rows
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 0
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 1 collaborators
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 0
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 03:56:33 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 03:56:39 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:56:39 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:56:40 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 03:56:40 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 03:56:40 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 03:56:40 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 03:56:40 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 03:56:40 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 03:56:40 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 03:56:41 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 03:56:52 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:56:52 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:56:53 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 03:56:53 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 03:57:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:57:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:57:06 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 03:57:06 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 03:57:06 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 03:57:06 - ui.modules.suivi_generator_module - ERROR - cleanup:531 - Error during module cleanup: 'NoneType' object has no attribute 'stop_auto_save'
2025-06-09 03:57:06 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 03:57:06 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 03:57:06 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 03:57:06 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 03:57:06 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 03:57:11 - ui.components.file_import - INFO - _load_file:172 - No file selected
2025-06-09 03:57:20 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx
2025-06-09 03:57:20 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx (18 rows)
2025-06-09 03:57:20 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 79312, Commune: SELIGNE
2025-06-09 03:57:20 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-09 03:57:20 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-09 03:57:20 - ui.modules.suivi_generator_module - INFO - on_success:306 - MOAI file processed successfully
2025-06-09 03:57:24 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-09 03:57:24 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-09 03:57:24 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-09 03:57:24 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-09 03:57:24 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 82 rows remaining
2025-06-09 03:57:24 - ui.modules.suivi_generator_module - INFO - on_success:328 - QGis file processed successfully
2025-06-09 03:57:28 - utils.file_utils - INFO - generate_teams_folder_path:453 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test
2025-06-09 03:57:28 - utils.file_utils - INFO - create_teams_folder:538 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test
2025-06-09 03:57:28 - utils.file_utils - INFO - generate_teams_folder_path:453 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test
2025-06-09 03:57:28 - utils.file_utils - INFO - get_teams_file_path:564 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test\Suivi_SELIGNE_test_79312.xlsx
2025-06-09 03:57:29 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test\Suivi_SELIGNE_test_79312.xlsx
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: test-CM Adresse
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: test-CM Adresse
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['O'] in sheet: test-Plan Adressage
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: test-Plan Adressage
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: test-Informations Commune
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: test-Informations Commune
2025-06-09 03:57:29 - core.excel_generator - INFO - _apply_plan_adressage_special_styling:301 - Special Plan Adressage styling applied to sheet: test-Plan Adressage
2025-06-09 03:57:29 - core.excel_generator - INFO - _add_data_validations:483 - Data validations added to CM Adresse sheet
2025-06-09 03:57:29 - core.excel_generator - INFO - _create_validation_sheet:509 - Validation sheet created
2025-06-09 03:57:29 - core.excel_generator - INFO - _add_duration_formula:570 - Duration formulas added
2025-06-09 03:57:29 - core.excel_generator - INFO - _add_commune_validations:615 - Commune validations added
2025-06-09 03:57:29 - core.excel_generator - INFO - _add_plan_adressage_validations:648 - Plan Adressage validations added
2025-06-09 03:57:29 - core.excel_generator - INFO - generate_excel_file:86 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test\Suivi_SELIGNE_test_79312.xlsx
2025-06-09 03:57:32 - ui.components.generation - INFO - show_generation_complete:356 - Opened folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SELIGNE_test
2025-06-09 03:58:33 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:58:34 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:58:41 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 03:58:41 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 03:58:41 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 03:58:41 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 03:58:41 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 03:58:41 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 03:58:41 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 03:58:41 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 03:58:42 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 6 communes to update
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:935 - Updating existing global Excel file
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:951 - Applied date formatting to existing Suivi Tickets data
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:964 - Applied date formatting to existing Traitement CMS Adr data
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:977 - Applied date formatting to existing Traitement PA data
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date d'affectation' in Suivi Tickets
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date Livraison' in Suivi Tickets
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date Dépose Ticket 501/511' in Suivi Tickets
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Dépose Ticket UPR' in Suivi Tickets
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Suivi Tickets
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in ESPLAS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in MONTCHALONS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SELIGNE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SURDOUX: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1204 - Applied date formatting to new CM Adresse data: 89 rows
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1746 - Applying aggressive date formatting to Traitement CMS Adr for columns: ['Date affectation', 'Date traitement', 'Date livraison']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date affectation' in Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date traitement' in Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date livraison' in Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date affectation' in Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date traitement' in Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date livraison' in Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Traitement CMS Adr
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in ESPLAS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in MONTCHALONS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SELIGNE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SURDOUX: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1318 - Applied date formatting to new Plan Adressage data: 496 rows
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1746 - Applying aggressive date formatting to Traitement PA for columns: ['Date traitement']
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date traitement' in Traitement PA
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date traitement' in Traitement PA
2025-06-09 03:58:44 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Traitement PA
2025-06-09 03:58:45 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Suivi Tickets
2025-06-09 03:58:45 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Traitement CMS Adr
2025-06-09 03:58:45 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Traitement PA
2025-06-09 03:58:45 - ui.modules.suivi_global_module - INFO - _format_global_excel:1466 - Global Excel file formatted successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 03:58:45 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:990 - Global Excel file updated: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 03:58:51 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:58:51 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:58:52 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 03:58:52 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 03:58:52 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 03:58:52 - ui.modules.team_stats_module - INFO - cleanup:1790 - Team Stats module cleaned up
2025-06-09 03:58:52 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 03:58:52 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 03:58:52 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 03:58:52 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 03:58:52 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 98 rows
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 553 rows
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 0
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 1 collaborators
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 0
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 03:58:53 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1802 - Dependencies verified successfully
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1819 - Starting CTJ export for Toute l'équipe, Juin 2025
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1892 - Team export: Total PA data rows: 553
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1917 - Team export: Found collaborators: ['ELJ Wissem']
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1970 - Team export: Processed 496 rows, found 253 valid dates for Juin 2025
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1991 - Team export: Generated 30 days of data, total CTJ: 253
2025-06-09 03:58:57 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1842 - CTJ data calculated: 30 records
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2573 - Creating team export, data length: 30
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2627 - Team export: found 1 collaborators: ['ELJ Wissem']
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 253 total CTJ values for ELJ Wissem
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _add_team_summary:2834 - Added team summary: Total CTJ=253, 1 collaborators
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2731 - Team stacked export sheet created successfully
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:2125 - Default sheet removed successfully
2025-06-09 03:59:03 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:2159 - Streamlined CTJ Excel file created successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_Toute_léquipe_20250609.xlsx
2025-06-09 03:59:04 - ui.modules.team_stats_module - INFO - _create_ctj_excel_file:2084 - CTJ Excel file created: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_Toute_léquipe_20250609.xlsx
2025-06-09 03:59:06 - __main__ - INFO - start_main_application:88 - Application closed normally
2025-06-09 09:02:41 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 09:02:41 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 09:02:41 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 09:02:41 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 09:02:41 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 09:02:41 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 09:02:41 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 09:02:41 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 09:02:41 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 09:02:41 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 09:02:41 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 09:02:43 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 09:02:43 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 09:02:43 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 09:02:43 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 09:02:44 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI202762\logo_Sofrecom.png
2025-06-09 09:02:44 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 09:02:44 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 09:02:44 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 09:02:44 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:02:44 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 09:02:44 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 09:02:44 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 09:02:45 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:02:45 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 09:02:45 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 09:02:45 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 09:02:45 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 09:02:45 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 09:02:45 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 09:02:48 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 09:02:48 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 09:02:48 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 09:02:48 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 09:02:49 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 09:02:49 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 09:02:49 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 09:02:49 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 09:02:50 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:02:50 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:02:51 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 09:02:51 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 09:02:51 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 09:02:51 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:02:51 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 09:02:51 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 09:02:51 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 09:02:51 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 09:02:55 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 6 communes to update
2025-06-09 09:02:57 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:02:57 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:02:58 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 09:02:58 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 09:02:58 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 09:02:58 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:02:58 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 09:02:58 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 09:02:58 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 09:02:58 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 98 rows
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 553 rows
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 0
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 1 collaborators
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 0
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 09:05:05 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 09:09:39 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:09:40 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:20:14 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:20:15 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:20:15 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 09:20:16 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 09:20:16 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:20:17 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:21:01 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 09:21:01 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 09:21:01 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 09:21:01 - ui.modules.team_stats_module - INFO - cleanup:1790 - Team Stats module cleaned up
2025-06-09 09:21:01 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:21:01 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 09:21:01 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 09:21:01 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 09:21:01 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 6
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {}
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 3 collaborators
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 6
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {}
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 09:21:02 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 09:28:12 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:28:12 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:28:13 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 09:28:14 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 09:28:14 - __main__ - INFO - start_main_application:88 - Application closed normally
2025-06-09 09:37:54 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 09:37:54 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 09:37:54 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 09:37:54 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 09:37:54 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 09:37:54 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 09:37:54 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 09:37:54 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 09:37:54 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 09:37:54 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 09:37:54 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 09:37:56 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 09:37:56 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 09:37:56 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 09:37:56 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 09:37:56 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI239922\logo_Sofrecom.png
2025-06-09 09:37:56 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 09:37:56 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 09:37:56 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 09:37:56 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:37:56 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 09:37:56 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 09:37:56 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 09:37:57 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:37:57 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 09:37:57 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 09:37:57 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 09:37:57 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 09:37:57 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 09:37:57 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 09:38:10 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 09:38:10 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 09:38:10 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 09:38:10 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 09:38:10 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 09:38:10 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 09:38:10 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 09:38:10 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 09:38:25 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-09 09:38:25 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-09 09:38:25 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-09 09:38:25 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-09 09:38:25 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-09 09:38:25 - ui.modules.suivi_generator_module - INFO - on_success:306 - MOAI file processed successfully
2025-06-09 09:38:29 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-09 09:38:29 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-09 09:38:29 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-09 09:38:29 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-09 09:38:29 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-09 09:38:29 - ui.modules.suivi_generator_module - INFO - on_success:328 - QGis file processed successfully
2025-06-09 09:38:36 - utils.file_utils - INFO - generate_teams_folder_path:453 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test_lundi
2025-06-09 09:38:36 - utils.file_utils - INFO - create_teams_folder:538 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test_lundi
2025-06-09 09:38:36 - utils.file_utils - INFO - generate_teams_folder_path:453 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test_lundi
2025-06-09 09:38:36 - utils.file_utils - INFO - get_teams_file_path:564 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test_lundi\Suivi_SURDOUX_test_lundi_87193.xlsx
2025-06-09 09:38:42 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test_lundi\Suivi_SURDOUX_test_lundi_87193.xlsx
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: test_lundi-CM Adresse
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: test_lundi-CM Adresse
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['O'] in sheet: test_lundi-Plan Adressage
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: test_lundi-Plan Adressage
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: test_lundi-Informations Commune
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: test_lundi-Informations Commune
2025-06-09 09:38:42 - core.excel_generator - INFO - _apply_plan_adressage_special_styling:301 - Special Plan Adressage styling applied to sheet: test_lundi-Plan Adressage
2025-06-09 09:38:42 - core.excel_generator - INFO - _add_data_validations:483 - Data validations added to CM Adresse sheet
2025-06-09 09:38:42 - core.excel_generator - INFO - _create_validation_sheet:509 - Validation sheet created
2025-06-09 09:38:42 - core.excel_generator - INFO - _add_duration_formula:570 - Duration formulas added
2025-06-09 09:38:42 - core.excel_generator - INFO - _add_commune_validations:615 - Commune validations added
2025-06-09 09:38:42 - core.excel_generator - INFO - _add_plan_adressage_validations:648 - Plan Adressage validations added
2025-06-09 09:38:42 - core.excel_generator - INFO - generate_excel_file:86 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test_lundi\Suivi_SURDOUX_test_lundi_87193.xlsx
2025-06-09 09:39:27 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:39:27 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:39:32 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 09:39:32 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 09:39:32 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 09:39:32 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:39:32 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 09:39:32 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 09:39:32 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 09:39:32 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 09:39:37 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 7 communes to update
2025-06-09 09:43:09 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:43:09 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:43:24 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 09:43:24 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 09:43:24 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 09:43:24 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:43:24 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 09:43:24 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 09:43:25 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 09:43:25 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 09:43:30 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:43:30 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:43:33 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 09:43:33 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 09:43:33 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 09:43:33 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:43:33 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 09:43:33 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 09:43:33 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 09:43:33 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 6
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {}
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 3 collaborators
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 6
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {}
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 09:43:36 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1802 - Dependencies verified successfully
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1819 - Starting CTJ export for Toute l'équipe, Juin 2025
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1892 - Team export: Total PA data rows: 496
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1917 - Team export: Found collaborators: ['ELJ Wissem', 'ZAOUGA Wissem', 'BACHOUEL IHEB']
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1970 - Team export: Processed 496 rows, found 496 valid dates for Juin 2025
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1991 - Team export: Generated 30 days of data, total CTJ: 496
2025-06-09 09:44:55 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1842 - CTJ data calculated: 30 records
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2573 - Creating team export, data length: 30
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2627 - Team export: found 3 collaborators: ['BACHOUEL IHEB', 'ELJ Wissem', 'ZAOUGA Wissem']
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 139 total CTJ values for BACHOUEL IHEB
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 136 total CTJ values for ELJ Wissem
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 221 total CTJ values for ZAOUGA Wissem
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _add_team_summary:2834 - Added team summary: Total CTJ=496, 3 collaborators
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2731 - Team stacked export sheet created successfully
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:2125 - Default sheet removed successfully
2025-06-09 09:45:02 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:2159 - Streamlined CTJ Excel file created successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_Toute_léquipe_20250609.xlsx
2025-06-09 09:45:03 - ui.modules.team_stats_module - INFO - _create_ctj_excel_file:2084 - CTJ Excel file created: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_Toute_léquipe_20250609.xlsx
2025-06-09 09:46:58 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:46:58 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:47:03 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 09:47:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 09:47:03 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 09:47:03 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:47:03 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 09:47:03 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 09:47:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 09:47:03 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 09:47:04 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 09:47:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 09:47:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 09:47:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 09:47:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 09:47:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 09:47:05 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 09:47:05 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 09:47:05 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 7 communes to update
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:935 - Updating existing global Excel file
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:951 - Applied date formatting to existing Suivi Tickets data
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:964 - Applied date formatting to existing Traitement CMS Adr data
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:977 - Applied date formatting to existing Traitement PA data
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date d'affectation' in Suivi Tickets
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date Livraison' in Suivi Tickets
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date Dépose Ticket 501/511' in Suivi Tickets
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Dépose Ticket UPR' in Suivi Tickets
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Suivi Tickets
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in ESPLAS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in MONTCHALONS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SELIGNE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SURDOUX: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SURDOUX: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1204 - Applied date formatting to new CM Adresse data: 98 rows
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1746 - Applying aggressive date formatting to Traitement CMS Adr for columns: ['Date affectation', 'Date traitement', 'Date livraison']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date affectation' in Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date traitement' in Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date livraison' in Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date affectation' in Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date traitement' in Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date livraison' in Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in ESPLAS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in MONTCHALONS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SELIGNE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SURDOUX: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SURDOUX: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1318 - Applied date formatting to new Plan Adressage data: 553 rows
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1746 - Applying aggressive date formatting to Traitement PA for columns: ['Date traitement']
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date traitement' in Traitement PA
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date traitement' in Traitement PA
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Traitement PA
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Suivi Tickets
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Traitement CMS Adr
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Traitement PA
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _format_global_excel:1466 - Global Excel file formatted successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 09:47:07 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:990 - Global Excel file updated: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 09:47:45 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:47:46 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:47:46 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 09:47:46 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 09:47:47 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 09:47:47 - ui.modules.team_stats_module - INFO - cleanup:1790 - Team Stats module cleaned up
2025-06-09 09:47:47 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:47:47 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 09:47:47 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 09:47:47 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 09:47:47 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 09:47:47 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 7 rows
2025-06-09 09:47:47 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 98 rows
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 553 rows
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (7, 18)
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 0
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 1 collaborators
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 0
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 09:47:48 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 09:47:58 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:47:58 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:48:02 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 09:48:02 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 09:48:03 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 09:48:03 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 09:48:03 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 09:48:03 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 09:48:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 09:48:03 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 09:48:08 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:48:08 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:48:19 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:48:19 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:48:35 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:48:35 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:48:36 - ui.home_screen - INFO - _open_teams_folder_directly:377 - User clicked Teams folder button - opening directly
2025-06-09 09:48:37 - ui.home_screen - INFO - _open_teams_folder_directly:386 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-09 09:48:37 - ui.home_screen - INFO - _open_teams_folder_directly:377 - User clicked Teams folder button - opening directly
2025-06-09 09:48:38 - ui.home_screen - INFO - _open_teams_folder_directly:386 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-09 09:49:20 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 09:49:20 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 09:49:20 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 09:49:20 - ui.modules.suivi_generator_module - ERROR - cleanup:531 - Error during module cleanup: 'NoneType' object has no attribute 'stop_auto_save'
2025-06-09 09:49:20 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 09:49:20 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 09:49:20 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 09:49:20 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 09:49:20 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 09:49:21 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:49:22 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:49:25 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:49:25 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:49:43 - __main__ - INFO - start_main_application:88 - Application closed normally
2025-06-09 09:50:01 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 09:50:01 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 09:50:01 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 09:50:01 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 09:50:01 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 09:50:01 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 09:50:01 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 09:50:01 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 09:50:01 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 09:50:01 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 09:50:01 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 09:50:03 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 09:50:03 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 09:50:03 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 09:50:03 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 09:50:03 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI49242\logo_Sofrecom.png
2025-06-09 09:50:03 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 09:50:03 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 09:50:03 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 09:50:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:50:04 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 09:50:04 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 09:50:04 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 09:50:05 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:50:05 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 09:50:05 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 09:50:05 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 09:50:05 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 09:50:05 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 09:50:05 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 09:50:09 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 09:50:09 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 09:50:10 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:50:11 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:50:15 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 09:50:15 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 09:50:26 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:50:27 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:51:03 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 09:51:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 09:51:03 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 09:51:03 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 09:51:03 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 09:51:03 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 09:51:03 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 09:51:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 09:51:16 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 09:51:16 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 09:51:22 - __main__ - INFO - start_main_application:88 - Application closed normally
2025-06-09 10:02:50 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 10:02:50 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 10:02:50 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 10:02:50 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 10:02:50 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 10:02:50 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 10:02:50 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 10:02:50 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 10:02:50 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 10:02:50 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 10:02:50 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 10:02:52 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 10:02:52 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 10:02:52 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 10:02:52 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 10:02:52 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI48402\logo_Sofrecom.png
2025-06-09 10:02:52 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 10:02:52 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 10:02:52 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 10:02:52 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:02:52 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 10:02:52 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 10:02:52 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 10:02:53 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:02:53 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 10:02:53 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 10:02:53 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 10:02:53 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 10:02:53 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 10:02:53 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 10:02:57 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:02:57 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:02:58 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 10:02:58 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 10:02:59 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:03:00 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:03:00 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 10:03:00 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 10:03:00 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 10:03:00 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 10:03:00 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 10:03:00 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 10:03:00 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 10:03:00 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 10:03:02 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 10:03:02 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 10:03:02 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 10:03:02 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 10:03:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 10:03:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 10:03:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:03:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:03:03 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 7 communes to update
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:03:35 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 6 communes to update
2025-06-09 10:03:36 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:935 - Updating existing global Excel file
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:36 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:951 - Applied date formatting to existing Suivi Tickets data
2025-06-09 10:03:36 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:964 - Applied date formatting to existing Traitement CMS Adr data
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:977 - Applied date formatting to existing Traitement PA data
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date d'affectation' in Suivi Tickets
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date Livraison' in Suivi Tickets
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date Dépose Ticket 501/511' in Suivi Tickets
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Dépose Ticket UPR' in Suivi Tickets
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1649 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Suivi Tickets
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in ESPLAS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in MONTCHALONS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SELIGNE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1163 - CM Adresse columns in SURDOUX: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1204 - Applied date formatting to new CM Adresse data: 89 rows
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1746 - Applying aggressive date formatting to Traitement CMS Adr for columns: ['Date affectation', 'Date traitement', 'Date livraison']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date affectation' in Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date traitement' in Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date livraison' in Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date affectation' in Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date traitement' in Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date livraison' in Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in ESPLAS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in MONTCHALONS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SELIGNE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1277 - Plan Adressage columns in SURDOUX: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1318 - Applied date formatting to new Plan Adressage data: 496 rows
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1746 - Applying aggressive date formatting to Traitement PA for columns: ['Date traitement']
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1751 - Processing column 'Date traitement' in Traitement PA
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1706 - Validating and formatting date column 'Date traitement' in Traitement PA
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1440 - Formatted INSEE columns as text in sheet: Traitement PA
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Suivi Tickets
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Traitement CMS Adr
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_sheet:1529 - Formatting applied to sheet: Traitement PA
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _format_global_excel:1466 - Global Excel file formatted successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 10:03:37 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:990 - Global Excel file updated: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 10:03:40 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:03:40 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:03:42 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 10:03:42 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 10:03:42 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 10:03:42 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 10:03:42 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 10:03:42 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 10:03:42 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 10:03:42 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:03:43 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 6 communes to update
2025-06-09 10:03:44 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:03:44 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:03:45 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 10:03:45 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 10:03:45 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 10:03:45 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 10:03:45 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 10:03:45 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 10:03:45 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 10:03:45 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 0
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 1 collaborators
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 0
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 10:03:46 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 10:03:49 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:03:49 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:03:50 - __main__ - INFO - start_main_application:88 - Application closed normally
2025-06-09 10:24:18 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 10:24:18 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 10:24:18 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 10:24:18 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 10:24:18 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 10:24:18 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 10:24:18 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 10:24:18 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 10:24:18 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 10:24:18 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 10:24:18 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 10:24:20 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 10:24:20 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 10:24:20 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 10:24:20 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 10:24:20 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI74682\logo_Sofrecom.png
2025-06-09 10:24:20 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 10:24:20 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 10:24:20 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 10:24:20 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:24:20 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 10:24:20 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 10:24:20 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 10:24:21 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:24:21 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 10:24:21 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 10:24:21 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 10:24:21 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 10:24:21 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 10:24:21 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 10:24:27 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 10:24:27 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 10:24:27 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 10:24:27 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 10:24:27 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 10:24:27 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 10:24:28 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 10:24:28 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 10:24:37 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:24:37 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:26:39 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 10:26:39 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 10:26:39 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 10:26:39 - ui.modules.suivi_generator_module - ERROR - cleanup:531 - Error during module cleanup: 'NoneType' object has no attribute 'stop_auto_save'
2025-06-09 10:26:39 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 10:26:39 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 10:26:39 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 10:26:39 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 10:26:39 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 10:26:59 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-09 10:26:59 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-09 10:26:59 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-09 10:26:59 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-09 10:26:59 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-09 10:26:59 - ui.modules.suivi_generator_module - INFO - on_success:306 - MOAI file processed successfully
2025-06-09 10:27:18 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-09 10:27:18 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-09 10:27:18 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-09 10:27:18 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-09 10:27:18 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-09 10:27:18 - ui.modules.suivi_generator_module - INFO - on_success:328 - QGis file processed successfully
2025-06-09 10:27:28 - utils.file_utils - INFO - generate_teams_folder_path:453 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_Test_MBA
2025-06-09 10:27:28 - utils.file_utils - INFO - create_teams_folder:538 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_Test_MBA
2025-06-09 10:27:28 - utils.file_utils - INFO - generate_teams_folder_path:453 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_Test_MBA
2025-06-09 10:27:28 - utils.file_utils - INFO - get_teams_file_path:564 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_Test_MBA\Suivi_SURDOUX_Test_MBA_87193.xlsx
2025-06-09 10:27:36 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_Test_MBA\Suivi_SURDOUX_Test_MBA_87193.xlsx
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: Test_MBA-CM Adresse
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: Test_MBA-CM Adresse
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['O'] in sheet: Test_MBA-Plan Adressage
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: Test_MBA-Plan Adressage
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: Test_MBA-Informations Commune
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: Test_MBA-Informations Commune
2025-06-09 10:27:36 - core.excel_generator - INFO - _apply_plan_adressage_special_styling:301 - Special Plan Adressage styling applied to sheet: Test_MBA-Plan Adressage
2025-06-09 10:27:36 - core.excel_generator - INFO - _add_data_validations:483 - Data validations added to CM Adresse sheet
2025-06-09 10:27:36 - core.excel_generator - INFO - _create_validation_sheet:509 - Validation sheet created
2025-06-09 10:27:36 - core.excel_generator - INFO - _add_duration_formula:570 - Duration formulas added
2025-06-09 10:27:36 - core.excel_generator - INFO - _add_commune_validations:615 - Commune validations added
2025-06-09 10:27:36 - core.excel_generator - INFO - _add_plan_adressage_validations:648 - Plan Adressage validations added
2025-06-09 10:27:36 - core.excel_generator - INFO - generate_excel_file:86 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_Test_MBA\Suivi_SURDOUX_Test_MBA_87193.xlsx
2025-06-09 10:33:32 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:33:32 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:33:36 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 10:33:36 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 10:33:36 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 10:33:36 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 10:33:36 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 10:33:36 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 10:33:36 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 10:33:36 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:33:48 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 7 communes to update
2025-06-09 10:35:54 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:35:54 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:35:55 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 10:35:55 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 10:35:55 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 10:35:55 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 10:35:55 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 10:35:55 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 10:35:56 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 10:35:56 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _load_existing_communes:525 - Loaded 6 existing communes for comparison
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ARIES ESPENAN
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: ESPLAS
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: MONTCHALONS
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SELIGNE
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _process_commune_folders:653 - Processed commune: SURDOUX
2025-06-09 10:35:58 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:547 - Analysis: 0 new communes, 7 communes to update
2025-06-09 10:36:23 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:36:23 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:36:29 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 10:36:29 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 10:36:29 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 10:36:29 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 10:36:29 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 10:36:29 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 10:36:29 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 10:36:30 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _load_global_data:692 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1253 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1254 - DataFrame shape: (6, 18)
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1275 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1276 - Has 'Date Livraison' column: True
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1335 - Communes traitées ce mois: 6
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1336 - Communes autres statuts: {}
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1241 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1080 - Analyzed statistics for 3 collaborators
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _update_overview_display:1398 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _update_overview_display:1399 - Communes traitées mois courant: 6
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _update_overview_display:1400 - Communes autres statuts: {}
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _update_export_filters:3160 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 10:36:33 - ui.modules.team_stats_module - INFO - _load_global_data:722 - Global data loaded and analyzed successfully
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1802 - Dependencies verified successfully
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1819 - Starting CTJ export for Toute l'équipe, Juin 2025
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1892 - Team export: Total PA data rows: 496
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1917 - Team export: Found collaborators: ['ELJ Wissem', 'ZAOUGA Wissem', 'BACHOUEL IHEB']
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1970 - Team export: Processed 496 rows, found 496 valid dates for Juin 2025
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1991 - Team export: Generated 30 days of data, total CTJ: 496
2025-06-09 10:39:19 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1842 - CTJ data calculated: 30 records
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2573 - Creating team export, data length: 30
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2627 - Team export: found 3 collaborators: ['BACHOUEL IHEB', 'ELJ Wissem', 'ZAOUGA Wissem']
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 139 total CTJ values for BACHOUEL IHEB
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 136 total CTJ values for ELJ Wissem
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2676 - Team export: wrote 221 total CTJ values for ZAOUGA Wissem
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _add_team_summary:2834 - Added team summary: Total CTJ=496, 3 collaborators
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_team_stacked_export:2731 - Team stacked export sheet created successfully
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:2125 - Default sheet removed successfully
2025-06-09 10:39:26 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:2159 - Streamlined CTJ Excel file created successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_Toute_léquipe_20250609.xlsx
2025-06-09 10:39:28 - ui.modules.team_stats_module - INFO - _create_ctj_excel_file:2084 - CTJ Excel file created: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_Toute_léquipe_20250609.xlsx
2025-06-09 10:43:18 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:43:18 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:44:42 - __main__ - INFO - start_main_application:88 - Application closed normally
2025-06-09 10:51:24 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 10:51:24 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 10:51:24 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 10:51:24 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 10:51:24 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 10:51:24 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 10:51:24 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 10:51:24 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 10:51:24 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 10:51:24 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 10:51:24 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 10:51:26 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 10:51:26 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 10:51:26 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 10:51:26 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 10:51:26 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI194322\logo_Sofrecom.png
2025-06-09 10:51:26 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 10:51:26 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 10:51:26 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 10:51:26 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:51:26 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 10:51:26 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 10:51:26 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 10:51:27 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:51:27 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 10:51:27 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 10:51:27 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 10:51:27 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 10:51:27 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 10:51:27 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 10:51:28 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 10:51:28 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 10:51:28 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 10:51:28 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 10:51:28 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 10:51:28 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 10:51:28 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 10:51:28 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-09 10:51:30 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: about
2025-06-09 10:51:30 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: about
2025-06-09 10:51:31 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 10:51:31 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 10:51:33 - __main__ - INFO - start_main_application:88 - Application closed normally
