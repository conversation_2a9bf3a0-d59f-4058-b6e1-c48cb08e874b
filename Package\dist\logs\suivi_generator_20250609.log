2025-06-09 03:55:09 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 03:55:09 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 03:55:09 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 03:55:09 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 03:55:09 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 03:55:09 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 03:55:09 - ui.splash_screen - INFO - _init_environment:275 - ============================================================
2025-06-09 03:55:09 - ui.splash_screen - INFO - _init_environment:276 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 03:55:09 - ui.splash_screen - INFO - _init_environment:277 - Version: 2.1
2025-06-09 03:55:09 - ui.splash_screen - INFO - _init_environment:278 - Author: Equipe BLI
2025-06-09 03:55:09 - ui.splash_screen - INFO - _init_environment:279 - ============================================================
2025-06-09 03:55:12 - __main__ - INFO - start_main_application:80 - Creating main application...
2025-06-09 03:55:12 - ui.main_window - INFO - _setup_window:70 - Window maximized using 'zoomed' state
2025-06-09 03:55:12 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 03:55:12 - ui.main_window - INFO - _setup_window:95 - Main window configured
2025-06-09 03:55:13 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI222642\logo_Sofrecom.png
2025-06-09 03:55:13 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 03:55:13 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 03:55:13 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 03:55:13 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:55:13 - ui.main_window - INFO - _set_window_icon:209 - Window icon set with PNG multi-size: 7 sizes
2025-06-09 03:55:13 - ui.main_window - INFO - _set_window_icon:216 - Additional ICO icon set for Windows compatibility
2025-06-09 03:55:13 - ui.main_window - INFO - _set_windows_taskbar_icon:282 - Windows taskbar icon set via API
2025-06-09 03:55:14 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:55:14 - ui.main_window - INFO - _setup_navigation:138 - Navigation system initialized
2025-06-09 03:55:14 - __main__ - INFO - start_main_application:83 - Application created successfully
2025-06-09 03:55:14 - __main__ - INFO - start_main_application:84 - Starting main loop...
2025-06-09 03:55:14 - ui.main_window - INFO - run:292 - Starting application main loop
2025-06-09 03:55:14 - ui.main_window - INFO - _post_init:156 - Window state after initialization: zoomed
2025-06-09 03:55:14 - ui.main_window - INFO - _post_init:164 - Main window initialization complete
2025-06-09 03:55:26 - __main__ - INFO - start_main_application:88 - Application closed normally
